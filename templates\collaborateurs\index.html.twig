{% extends 'base.html.twig' %}

{% block title %}Collaborateurs - OSI Manager{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0">
    <!-- En-tête -->
    <div class="sm:flex sm:items-center mb-8">
        <div class="sm:flex-auto">
            <h1 class="text-3xl font-bold text-gray-900">Collaborateurs</h1>
            <p class="mt-2 text-gray-600">Gestion de l'équipe et des horaires</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button type="button" onclick="openModal('addCollaborateurModal')" class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto">
                Ajouter un collaborateur
            </button>
        </div>
    </div>

    <!-- Filtres -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700">Rechercher</label>
                    <input type="text" id="search" name="search" placeholder="Nom ou prénom..." class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700">Rôle</label>
                    <select id="role" name="role" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Tous les rôles</option>
                        <option value="Manager">Manager</option>
                        <option value="Consultant">Consultant</option>
                        <option value="Expert">Expert</option>
                        <option value="Technicien">Technicien</option>
                    </select>
                </div>
                <div>
                    <label for="forfaitJour" class="block text-sm font-medium text-gray-700">Type de contrat</label>
                    <select id="forfaitJour" name="forfaitJour" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Tous</option>
                        <option value="true">Forfait jour</option>
                        <option value="false">Horaire</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des collaborateurs -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <ul role="list" class="divide-y divide-gray-200">
            {% for collaborateur in collaborateurs %}
                <li>
                    <div class="px-4 py-4 flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                                    <span class="text-sm font-medium text-white">
                                        {{ collaborateur.prenom|first }}{{ collaborateur.nom|first }}
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="flex items-center">
                                    <p class="text-sm font-medium text-gray-900">{{ collaborateur.nomComplet }}</p>
                                    {% if collaborateur.forfaitJour %}
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Forfait jour
                                        </span>
                                    {% endif %}
                                </div>
                                <div class="flex items-center text-sm text-gray-500">
                                    <p>{{ collaborateur.role }}</p>
                                    <span class="mx-2">•</span>
                                    <p>{{ collaborateur.email }}</p>
                                    <span class="mx-2">•</span>
                                    <p>{{ collaborateur.horaireHebdo }}h/semaine</p>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <a href="{{ path('app_collaborateur_detail', {id: collaborateur.id}) }}" class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                Voir détail
                            </a>
                            <button type="button" onclick="editCollaborateur({{ collaborateur.id }})" class="text-gray-600 hover:text-gray-900 text-sm font-medium">
                                Modifier
                            </button>
                        </div>
                    </div>
                </li>
            {% else %}
                <li class="px-4 py-8 text-center text-gray-500">
                    Aucun collaborateur trouvé
                </li>
            {% endfor %}
        </ul>
    </div>
</div>

<!-- Modal d'ajout de collaborateur -->
<div id="addCollaborateurModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Ajouter un collaborateur</h3>
            <form id="addCollaborateurForm">
                <div class="space-y-4">
                    <div>
                        <label for="nom" class="block text-sm font-medium text-gray-700">Nom</label>
                        <input type="text" id="nom" name="nom" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="prenom" class="block text-sm font-medium text-gray-700">Prénom</label>
                        <input type="text" id="prenom" name="prenom" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="email" id="email" name="email" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="roleModal" class="block text-sm font-medium text-gray-700">Rôle</label>
                        <select id="roleModal" name="role" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            <option value="">Sélectionner un rôle</option>
                            <option value="Manager">Manager</option>
                            <option value="Consultant">Consultant</option>
                            <option value="Expert">Expert</option>
                            <option value="Technicien">Technicien</option>
                        </select>
                    </div>
                    <div>
                        <label for="horaireHebdo" class="block text-sm font-medium text-gray-700">Horaire hebdomadaire</label>
                        <input type="number" id="horaireHebdo" name="horaireHebdo" min="0" max="168" value="35" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div class="flex items-center">
                        <input type="checkbox" id="forfaitJourModal" name="forfaitJour" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label for="forfaitJourModal" class="ml-2 block text-sm text-gray-900">Forfait jour</label>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeModal('addCollaborateurModal')" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                        Annuler
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                        Ajouter
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

function editCollaborateur(id) {
    // TODO: Implémenter la modification
    alert('Modification du collaborateur ' + id);
}

// Gestion du formulaire d'ajout
document.getElementById('addCollaborateurForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const data = {
        nom: formData.get('nom'),
        prenom: formData.get('prenom'),
        email: formData.get('email'),
        role: formData.get('role'),
        horaireHebdo: parseFloat(formData.get('horaireHebdo')),
        forfaitJour: formData.get('forfaitJour') === 'on'
    };
    
    try {
        const response = await axios.post("{{ path('api_collaborateur_create') }}", data);
        if (response.status === 201) {
            location.reload();
        }
    } catch (error) {
        alert('Erreur lors de l\'ajout du collaborateur');
        console.error(error);
    }
});

// Filtrage en temps réel
document.getElementById('search').addEventListener('input', filterCollaborateurs);
document.getElementById('role').addEventListener('change', filterCollaborateurs);
document.getElementById('forfaitJour').addEventListener('change', filterCollaborateurs);

function filterCollaborateurs() {
    // TODO: Implémenter le filtrage côté client ou via API
}
</script>
{% endblock %}
