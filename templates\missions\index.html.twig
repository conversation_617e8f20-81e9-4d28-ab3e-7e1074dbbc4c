{% extends 'base.html.twig' %}

{% block title %}Missions - OSI Manager{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0">
    <!-- En-tête -->
    <div class="sm:flex sm:items-center mb-8">
        <div class="sm:flex-auto">
            <h1 class="text-3xl font-bold text-gray-900">Missions</h1>
            <p class="mt-2 text-gray-600">Gestion des missions et déplacements</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button type="button" onclick="openModal('addMissionModal')" class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto">
                Nouvelle mission
            </button>
        </div>
    </div>

    <!-- Filtres -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <div class="grid grid-cols-1 gap-4 sm:grid-cols-4">
                <div>
                    <label for="searchMission" class="block text-sm font-medium text-gray-700">Rechercher</label>
                    <input type="text" id="searchMission" placeholder="Titre ou pays..." class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                </div>
                <div>
                    <label for="zoneFilter" class="block text-sm font-medium text-gray-700">Zone</label>
                    <select id="zoneFilter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Toutes les zones</option>
                        <option value="EURO">Zone Euro</option>
                        <option value="HORS_EURO">Hors Zone Euro</option>
                    </select>
                </div>
                <div>
                    <label for="niveauFilter" class="block text-sm font-medium text-gray-700">Niveau</label>
                    <select id="niveauFilter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Tous les niveaux</option>
                        <option value="1">Niveau 1</option>
                        <option value="2">Niveau 2</option>
                    </select>
                </div>
                <div>
                    <label for="statutFilter" class="block text-sm font-medium text-gray-700">Statut</label>
                    <select id="statutFilter" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        <option value="">Tous</option>
                        <option value="en_cours">En cours</option>
                        <option value="prochaines">Prochaines</option>
                        <option value="terminees">Terminées</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des missions -->
    <div class="bg-white shadow overflow-hidden sm:rounded-md">
        <ul role="list" class="divide-y divide-gray-200">
            {% for mission in missions %}
                <li>
                    <div class="px-4 py-4">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <div class="h-10 w-10 rounded-full bg-purple-500 flex items-center justify-center">
                                        <span class="text-sm font-medium text-white">🌍</span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="flex items-center">
                                        <p class="text-sm font-medium text-gray-900">{{ mission.titre }}</p>
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {% if mission.zone == 'EURO' %}bg-blue-100 text-blue-800{% else %}bg-green-100 text-green-800{% endif %}">
                                            {{ mission.zone == 'EURO' ? 'Zone Euro' : 'Hors Zone Euro' }}
                                        </span>
                                        <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                            Niveau {{ mission.niveau }}
                                        </span>
                                    </div>
                                    <div class="flex items-center text-sm text-gray-500 mt-1">
                                        <p>{{ mission.collaborateur.nomComplet }}</p>
                                        <span class="mx-2">•</span>
                                        <p>{{ mission.pays }}</p>
                                        <span class="mx-2">•</span>
                                        <p>{{ mission.dateDebut|date('d/m/Y') }} - {{ mission.dateFin|date('d/m/Y') }}</p>
                                        <span class="mx-2">•</span>
                                        <p>{{ mission.dureeJours }} jour(s)</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                {% set today = "now"|date("Y-m-d") %}
                                {% set dateDebut = mission.dateDebut|date("Y-m-d") %}
                                {% set dateFin = mission.dateFin|date("Y-m-d") %}

                                {% if dateDebut <= today and dateFin >= today %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        En cours
                                    </span>
                                {% elseif dateDebut > today %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Prochaine
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                                        Terminée
                                    </span>
                                {% endif %}

                                <a href="{{ path('app_mission_detail', {id: mission.id}) }}" class="text-blue-600 hover:text-blue-900 text-sm font-medium">
                                    Voir détail
                                </a>
                                <button type="button" onclick="editMission({{ mission.id }})" class="text-gray-600 hover:text-gray-900 text-sm font-medium">
                                    Modifier
                                </button>
                            </div>
                        </div>
                    </div>
                </li>
            {% else %}
                <li class="px-4 py-8 text-center text-gray-500">
                    Aucune mission trouvée
                </li>
            {% endfor %}
        </ul>
    </div>
</div>

<!-- Modal d'ajout de mission -->
<div id="addMissionModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Nouvelle mission</h3>
            <form id="addMissionForm">
                <div class="space-y-4">
                    <div>
                        <label for="collaborateurSelect" class="block text-sm font-medium text-gray-700">Collaborateur</label>
                        <select id="collaborateurSelect" name="collaborateurId" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            <option value="">Sélectionner un collaborateur</option>
                        </select>
                    </div>
                    <div>
                        <label for="titreMission" class="block text-sm font-medium text-gray-700">Titre</label>
                        <input type="text" id="titreMission" name="titre" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="paysMission" class="block text-sm font-medium text-gray-700">Pays</label>
                        <input type="text" id="paysMission" name="pays" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="dateDebutMission" class="block text-sm font-medium text-gray-700">Date début</label>
                            <input type="date" id="dateDebutMission" name="dateDebut" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        </div>
                        <div>
                            <label for="dateFinMission" class="block text-sm font-medium text-gray-700">Date fin</label>
                            <input type="date" id="dateFinMission" name="dateFin" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label for="niveauMission" class="block text-sm font-medium text-gray-700">Niveau</label>
                            <select id="niveauMission" name="niveau" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                <option value="">Sélectionner</option>
                                <option value="1">Niveau 1</option>
                                <option value="2">Niveau 2</option>
                            </select>
                        </div>
                        <div>
                            <label for="zoneMission" class="block text-sm font-medium text-gray-700">Zone</label>
                            <select id="zoneMission" name="zone" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                                <option value="">Sélectionner</option>
                                <option value="EURO">Zone Euro</option>
                                <option value="HORS_EURO">Hors Zone Euro</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeModal('addMissionModal')" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                        Annuler
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                        Créer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openModal(modalId) {
    document.getElementById(modalId).classList.remove('hidden');
}

function closeModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
}

function editMission(id) {
    // TODO: Implémenter la modification
    alert('Modification de la mission ' + id);
}

// Charger les collaborateurs au chargement de la page
document.addEventListener('DOMContentLoaded', async function() {
    try {
        const response = await axios.get("{{ path('api_collaborateur_index') }}");
        const select = document.getElementById('collaborateurSelect');

        response.data.forEach(collaborateur => {
            const option = document.createElement('option');
            option.value = collaborateur.id;
            option.textContent = `${collaborateur.nom} ${collaborateur.prenom} (${collaborateur.role})`;
            select.appendChild(option);
        });
    } catch (error) {
        console.error('Erreur lors du chargement des collaborateurs:', error);
    }
});

// Gestion du formulaire d'ajout
document.getElementById('addMissionForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const formData = new FormData(e.target);
    const data = {
        collaborateurId: parseInt(formData.get('collaborateurId')),
        titre: formData.get('titre'),
        pays: formData.get('pays'),
        dateDebut: formData.get('dateDebut'),
        dateFin: formData.get('dateFin'),
        niveau: parseInt(formData.get('niveau')),
        zone: formData.get('zone')
    };

    try {
        const response = await axios.post("{{ path('api_mission_create') }}", data);
        if (response.status === 201) {
            location.reload();
        }
    } catch (error) {
        alert('Erreur lors de la création de la mission');
        console.error(error);
    }
});
</script>
{% endblock %}
