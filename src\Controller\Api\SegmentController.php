<?php

namespace App\Controller\Api;

use App\Entity\Segment;
use App\Repository\SegmentRepository;
use App\Repository\MissionRepository;
use App\Service\ValidationService;
use App\Service\HeureCalculatorService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Symfony\Component\Validator\Validator\ValidatorInterface;

#[Route('/api/segments', name: 'api_segment_')]
class SegmentController extends AbstractController
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private SerializerInterface $serializer,
        private ValidatorInterface $validator,
        private SegmentRepository $segmentRepository,
        private MissionRepository $missionRepository,
        private ValidationService $validationService,
        private HeureCalculatorService $heureCalculator
    ) {}

    #[Route('', name: 'index', methods: ['GET'])]
    public function index(Request $request): JsonResponse
    {
        $type = $request->query->get('type');
        $missionId = $request->query->get('mission');
        $debut = $request->query->get('debut');
        $fin = $request->query->get('fin');

        if ($type) {
            $segments = $this->segmentRepository->findByType($type);
        } elseif ($missionId) {
            $mission = $this->missionRepository->find($missionId);
            $segments = $mission ? $this->segmentRepository->findByMission($mission) : [];
        } elseif ($debut && $fin) {
            $debutDate = new \DateTime($debut);
            $finDate = new \DateTime($fin);
            $segments = $this->segmentRepository->findInPeriod($debutDate, $finDate);
        } else {
            $segments = $this->segmentRepository->findAll();
        }

        return $this->json($segments, Response::HTTP_OK, [], [
            'groups' => ['segment:read']
        ]);
    }

    #[Route('/calendar', name: 'calendar', methods: ['GET'])]
    public function calendar(Request $request): JsonResponse
    {
        $debut = $request->query->get('start');
        $fin = $request->query->get('end');

        if (!$debut || !$fin) {
            return $this->json(['error' => 'Paramètres start et end requis'], Response::HTTP_BAD_REQUEST);
        }

        $debutDate = new \DateTime($debut);
        $finDate = new \DateTime($fin);

        $events = $this->segmentRepository->findForCalendar($debutDate, $finDate);

        return $this->json($events);
    }

    #[Route('/types/{type}', name: 'by_type', methods: ['GET'])]
    public function byType(string $type): JsonResponse
    {
        $validTypes = [Segment::TYPE_VOYAGE, Segment::TYPE_INTERVENTION, Segment::TYPE_STAND_BY];

        if (!in_array($type, $validTypes)) {
            return $this->json(['error' => 'Type de segment invalide'], Response::HTTP_BAD_REQUEST);
        }

        $segments = $this->segmentRepository->findByType($type);

        return $this->json($segments, Response::HTTP_OK, [], [
            'groups' => ['segment:read']
        ]);
    }

    #[Route('/hors-plages', name: 'hors_plages', methods: ['GET'])]
    public function horsPlages(): JsonResponse
    {
        $segments = $this->segmentRepository->findHorsPlagesNormales();

        return $this->json($segments, Response::HTTP_OK, [], [
            'groups' => ['segment:read']
        ]);
    }

    #[Route('/weekend', name: 'weekend', methods: ['GET'])]
    public function weekend(): JsonResponse
    {
        $segments = $this->segmentRepository->findWeekend();

        return $this->json($segments, Response::HTTP_OK, [], [
            'groups' => ['segment:read']
        ]);
    }

    #[Route('/{id}', name: 'show', methods: ['GET'])]
    public function show(Segment $segment): JsonResponse
    {
        return $this->json($segment, Response::HTTP_OK, [], [
            'groups' => ['segment:read', 'segment:detail']
        ]);
    }

    #[Route('', name: 'create', methods: ['POST'])]
    public function create(Request $request): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        $mission = $this->missionRepository->find($data['missionId'] ?? null);
        if (!$mission) {
            return $this->json(['error' => 'Mission non trouvée'], Response::HTTP_BAD_REQUEST);
        }

        $segment = new Segment();
        $segment->setMission($mission)
                ->setType($data['type'] ?? Segment::TYPE_INTERVENTION)
                ->setDateHeureDebut(new \DateTime($data['dateHeureDebut'] ?? 'now'))
                ->setDateHeureFin(new \DateTime($data['dateHeureFin'] ?? 'now'));

        $errors = $this->validator->validate($segment);
        if (count($errors) > 0) {
            return $this->json(['errors' => (string) $errors], Response::HTTP_BAD_REQUEST);
        }

        // Validation métier
        $validation = $this->validationService->validerSegment($segment);
        if (!$validation['valide']) {
            return $this->json([
                'errors' => $validation['erreurs'],
                'alertes' => $validation['alertes']
            ], Response::HTTP_BAD_REQUEST);
        }

        $this->entityManager->persist($segment);
        $this->entityManager->flush();

        return $this->json($segment, Response::HTTP_CREATED, [], [
            'groups' => ['segment:read']
        ]);
    }

    #[Route('/{id}', name: 'update', methods: ['PUT'])]
    public function update(Request $request, Segment $segment): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if (isset($data['type'])) $segment->setType($data['type']);
        if (isset($data['dateHeureDebut'])) $segment->setDateHeureDebut(new \DateTime($data['dateHeureDebut']));
        if (isset($data['dateHeureFin'])) $segment->setDateHeureFin(new \DateTime($data['dateHeureFin']));

        if (isset($data['missionId'])) {
            $mission = $this->missionRepository->find($data['missionId']);
            if (!$mission) {
                return $this->json(['error' => 'Mission non trouvée'], Response::HTTP_BAD_REQUEST);
            }
            $segment->setMission($mission);
        }

        $errors = $this->validator->validate($segment);
        if (count($errors) > 0) {
            return $this->json(['errors' => (string) $errors], Response::HTTP_BAD_REQUEST);
        }

        // Validation métier
        $validation = $this->validationService->validerSegment($segment);
        if (!$validation['valide']) {
            return $this->json([
                'errors' => $validation['erreurs'],
                'alertes' => $validation['alertes']
            ], Response::HTTP_BAD_REQUEST);
        }

        $this->entityManager->flush();

        return $this->json($segment, Response::HTTP_OK, [], [
            'groups' => ['segment:read']
        ]);
    }

    #[Route('/{id}', name: 'delete', methods: ['DELETE'])]
    public function delete(Segment $segment): JsonResponse
    {
        $this->entityManager->remove($segment);
        $this->entityManager->flush();

        return $this->json(null, Response::HTTP_NO_CONTENT);
    }



    #[Route('/{id}/duree', name: 'duree', methods: ['GET'])]
    public function duree(Segment $segment): JsonResponse
    {
        $dureeHeures = $this->heureCalculator->getDureeHeures($segment);
        $dureeMinutes = $segment->getDureeMinutes();

        return $this->json([
            'dureeHeures' => $dureeHeures,
            'dureeMinutes' => $dureeMinutes,
            'dureeFormatee' => sprintf('%dh%02d', floor($dureeHeures), ($dureeMinutes % 60))
        ]);
    }

    #[Route('/{id}/validation', name: 'validation', methods: ['GET'])]
    public function validation(Segment $segment): JsonResponse
    {
        $validation = $this->validationService->validerSegment($segment);

        return $this->json($validation);
    }
}
