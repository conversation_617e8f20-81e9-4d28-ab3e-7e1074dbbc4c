<?php

namespace App\Entity;

use ApiPlatform\Metadata\ApiResource;
use App\Repository\CollaborateurRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Serializer\Annotation\Groups;

#[ORM\Entity(repositoryClass: CollaborateurRepository::class)]
#[ApiResource]
class Collaborateur
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    #[Groups(['collaborateur:read'])]
    private ?int $id = null;

    #[ORM\Column(length: 100)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 100)]
    #[Groups(['collaborateur:read', 'collaborateur:write'])]
    private ?string $nom = null;

    #[ORM\Column(length: 100)]
    #[Assert\NotBlank]
    #[Assert\Length(max: 100)]
    #[Groups(['collaborateur:read', 'collaborateur:write'])]
    private ?string $prenom = null;

    #[ORM\Column(length: 180, unique: true)]
    #[Assert\NotBlank]
    #[Assert\Email]
    #[Groups(['collaborateur:read', 'collaborateur:write'])]
    private ?string $email = null;

    #[ORM\Column(length: 50)]
    #[Assert\NotBlank]
    #[Groups(['collaborateur:read', 'collaborateur:write'])]
    private ?string $role = null;

    #[ORM\Column]
    #[Assert\NotNull]
    #[Assert\Range(min: 0, max: 168)]
    #[Groups(['collaborateur:read', 'collaborateur:write'])]
    private ?float $horaireHebdo = null;

    #[ORM\Column]
    #[Groups(['collaborateur:read', 'collaborateur:write'])]
    private ?bool $forfaitJour = false;

    /**
     * @var Collection<int, Mission>
     */
    #[ORM\OneToMany(targetEntity: Mission::class, mappedBy: 'collaborateur', orphanRemoval: true)]
    #[Groups(['collaborateur:detail'])]
    private Collection $missions;

    /**
     * @var Collection<int, SemaineTravail>
     */
    #[ORM\OneToMany(targetEntity: SemaineTravail::class, mappedBy: 'collaborateur', orphanRemoval: true)]
    #[Groups(['collaborateur:detail'])]
    private Collection $semainesTravail;

    public function __construct()
    {
        $this->missions = new ArrayCollection();
        $this->semainesTravail = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getNom(): ?string
    {
        return $this->nom;
    }

    public function setNom(string $nom): static
    {
        $this->nom = $nom;
        return $this;
    }

    public function getPrenom(): ?string
    {
        return $this->prenom;
    }

    public function setPrenom(string $prenom): static
    {
        $this->prenom = $prenom;
        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): static
    {
        $this->email = $email;
        return $this;
    }

    public function getRole(): ?string
    {
        return $this->role;
    }

    public function setRole(string $role): static
    {
        $this->role = $role;
        return $this;
    }

    public function getHoraireHebdo(): ?float
    {
        return $this->horaireHebdo;
    }

    public function setHoraireHebdo(float $horaireHebdo): static
    {
        $this->horaireHebdo = $horaireHebdo;
        return $this;
    }

    public function isForfaitJour(): ?bool
    {
        return $this->forfaitJour;
    }

    public function setForfaitJour(bool $forfaitJour): static
    {
        $this->forfaitJour = $forfaitJour;
        return $this;
    }

    /**
     * @return Collection<int, Mission>
     */
    public function getMissions(): Collection
    {
        return $this->missions;
    }

    public function addMission(Mission $mission): static
    {
        if (!$this->missions->contains($mission)) {
            $this->missions->add($mission);
            $mission->setCollaborateur($this);
        }
        return $this;
    }

    public function removeMission(Mission $mission): static
    {
        if ($this->missions->removeElement($mission)) {
            if ($mission->getCollaborateur() === $this) {
                $mission->setCollaborateur(null);
            }
        }
        return $this;
    }

    /**
     * @return Collection<int, SemaineTravail>
     */
    public function getSemainesTravail(): Collection
    {
        return $this->semainesTravail;
    }

    public function addSemaineTravail(SemaineTravail $semaineTravail): static
    {
        if (!$this->semainesTravail->contains($semaineTravail)) {
            $this->semainesTravail->add($semaineTravail);
            $semaineTravail->setCollaborateur($this);
        }
        return $this;
    }

    public function removeSemaineTravail(SemaineTravail $semaineTravail): static
    {
        if ($this->semainesTravail->removeElement($semaineTravail)) {
            if ($semaineTravail->getCollaborateur() === $this) {
                $semaineTravail->setCollaborateur(null);
            }
        }
        return $this;
    }

    public function getNomComplet(): string
    {
        return $this->prenom . ' ' . $this->nom;
    }

    public function __toString(): string
    {
        return $this->getNomComplet();
    }
}
