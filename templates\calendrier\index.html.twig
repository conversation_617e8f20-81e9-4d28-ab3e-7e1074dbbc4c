{% extends 'base.html.twig' %}

{% block title %}Calendrier - OSI Manager{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.css' rel='stylesheet' />
{% endblock %}

{% block body %}
<div class="px-4 py-6 sm:px-0">
    <!-- En-tête -->
    <div class="sm:flex sm:items-center mb-8">
        <div class="sm:flex-auto">
            <h1 class="text-3xl font-bold text-gray-900">Calendrier des missions</h1>
            <p class="mt-2 text-gray-600">Gestion des segments de mission</p>
        </div>
        <div class="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
            <button type="button" onclick="openModal('addSegmentModal')" class="inline-flex items-center justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:w-auto">
                Ajouter un segment
            </button>
        </div>
    </div>

    <!-- Légende -->
    <div class="bg-white shadow rounded-lg mb-6">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Légende</h3>
            <div class="flex flex-wrap gap-4">
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-blue-500 rounded mr-2"></div>
                    <span class="text-sm text-gray-700">Voyage</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-green-500 rounded mr-2"></div>
                    <span class="text-sm text-gray-700">Intervention</span>
                </div>
                <div class="flex items-center">
                    <div class="w-4 h-4 bg-orange-500 rounded mr-2"></div>
                    <span class="text-sm text-gray-700">Stand-by</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Calendrier -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
            <div id="calendar"></div>
        </div>
    </div>
</div>

<!-- Modal d'ajout/modification de segment -->
<div id="addSegmentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4" id="modalTitle">Ajouter un segment</h3>
            <form id="segmentForm">
                <input type="hidden" id="segmentId" name="segmentId">
                <div class="space-y-4">
                    <div>
                        <label for="missionSelect" class="block text-sm font-medium text-gray-700">Mission</label>
                        <select id="missionSelect" name="missionId" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            <option value="">Sélectionner une mission</option>
                        </select>
                    </div>
                    <div>
                        <label for="typeSelect" class="block text-sm font-medium text-gray-700">Type</label>
                        <select id="typeSelect" name="type" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                            <option value="">Sélectionner un type</option>
                            <option value="VOYAGE">Voyage</option>
                            <option value="INTERVENTION">Intervention</option>
                            <option value="STAND_BY">Stand-by</option>
                        </select>
                    </div>
                    <div>
                        <label for="dateHeureDebut" class="block text-sm font-medium text-gray-700">Date et heure de début</label>
                        <input type="datetime-local" id="dateHeureDebut" name="dateHeureDebut" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                    <div>
                        <label for="dateHeureFin" class="block text-sm font-medium text-gray-700">Date et heure de fin</label>
                        <input type="datetime-local" id="dateHeureFin" name="dateHeureFin" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm">
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeModal('addSegmentModal')" class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200">
                        Annuler
                    </button>
                    <button type="button" id="deleteButton" onclick="deleteSegment()" class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 hidden">
                        Supprimer
                    </button>
                    <button type="submit" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700">
                        Enregistrer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src='https://cdn.jsdelivr.net/npm/fullcalendar@6.1.10/index.global.min.js'></script>
    <script>
        let calendar;
        let currentSegmentId = null;

        // URLs pour les appels API
        const apiUrls = {
            segmentUpdate: "{{ path('api_segment_update', {id: '__ID__'}) }}".replace('__ID__', ''),
            segmentDelete: "{{ path('api_segment_delete', {id: '__ID__'}) }}".replace('__ID__', ''),
            segmentCreate: "{{ path('api_segment_create') }}"
        };

        document.addEventListener('DOMContentLoaded', function() {
            const calendarEl = document.getElementById('calendar');

            calendar = new FullCalendar.Calendar(calendarEl, {
                initialView: 'timeGridWeek',
                locale: 'fr',
                headerToolbar: {
                    left: 'prev,next today',
                    center: 'title',
                    right: 'dayGridMonth,timeGridWeek,timeGridDay'
                },
                height: 'auto',
                slotMinTime: '06:00:00',
                slotMaxTime: '22:00:00',
                businessHours: [
                    {
                        daysOfWeek: [1, 2, 3, 4, 5],
                        startTime: '09:00',
                        endTime: '12:00'
                    },
                    {
                        daysOfWeek: [1, 2, 3, 4, 5],
                        startTime: '14:00',
                        endTime: '18:00'
                    }
                ],
                selectable: true,
                selectMirror: true,
                editable: true,
                eventResizableFromStart: true,
                events: function(info, successCallback, failureCallback) {
                    axios.get("{{ path('api_segment_calendar') }}", {
                        params: {
                            start: info.startStr,
                            end: info.endStr
                        }
                    })
                    .then(response => {
                        successCallback(response.data);
                    })
                    .catch(error => {
                        console.error('Erreur lors du chargement des événements:', error);
                        failureCallback(error);
                    });
                },
                select: function(info) {
                    openSegmentModal();
                    document.getElementById('dateHeureDebut').value = info.startStr.slice(0, 16);
                    document.getElementById('dateHeureFin').value = info.endStr.slice(0, 16);
                },
                eventClick: function(info) {
                    editSegment(info.event);
                },
                eventDrop: function(info) {
                    updateSegmentDates(info.event);
                },
                eventResize: function(info) {
                    updateSegmentDates(info.event);
                }
            });

            calendar.render();
            loadMissions();
        });

        function openModal(modalId) {
            document.getElementById(modalId).classList.remove('hidden');
        }

        function closeModal(modalId) {
            document.getElementById(modalId).classList.add('hidden');
            resetForm();
        }

        function openSegmentModal() {
            currentSegmentId = null;
            document.getElementById('modalTitle').textContent = 'Ajouter un segment';
            document.getElementById('deleteButton').classList.add('hidden');
            openModal('addSegmentModal');
        }

        function editSegment(event) {
            currentSegmentId = event.id;
            document.getElementById('modalTitle').textContent = 'Modifier le segment';
            document.getElementById('deleteButton').classList.remove('hidden');

            // Remplir le formulaire avec les données de l'événement
            document.getElementById('segmentId').value = event.id;
            document.getElementById('typeSelect').value = event.extendedProps.type;
            document.getElementById('dateHeureDebut').value = event.startStr.slice(0, 16);
            document.getElementById('dateHeureFin').value = event.endStr.slice(0, 16);

            openModal('addSegmentModal');
        }

        function resetForm() {
            document.getElementById('segmentForm').reset();
            currentSegmentId = null;
        }

        async function loadMissions() {
            try {
                const response = await axios.get("{{ path('api_mission_index') }}");
                const select = document.getElementById('missionSelect');
                select.innerHTML = '<option value="">Sélectionner une mission</option>';

                response.data.forEach(mission => {
                    const option = document.createElement('option');
                    option.value = mission.id;
                    option.textContent = `${mission.titre} - ${mission.collaborateur.nom} ${mission.collaborateur.prenom}`;
                    select.appendChild(option);
                });
            } catch (error) {
                console.error('Erreur lors du chargement des missions:', error);
            }
        }

        async function updateSegmentDates(event) {
            try {
                await axios.put(apiUrls.segmentUpdate + event.id, {
                    dateHeureDebut: event.startStr,
                    dateHeureFin: event.endStr
                });
            } catch (error) {
                console.error('Erreur lors de la mise à jour:', error);
                event.revert();
            }
        }

        async function deleteSegment() {
            if (!currentSegmentId) return;

            if (confirm('Êtes-vous sûr de vouloir supprimer ce segment ?')) {
                try {
                    await axios.delete(apiUrls.segmentDelete + currentSegmentId);
                    calendar.refetchEvents();
                    closeModal('addSegmentModal');
                } catch (error) {
                    console.error('Erreur lors de la suppression:', error);
                    alert('Erreur lors de la suppression du segment');
                }
            }
        }

        // Gestion du formulaire
        document.getElementById('segmentForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            const data = {
                missionId: parseInt(formData.get('missionId')),
                type: formData.get('type'),
                dateHeureDebut: formData.get('dateHeureDebut'),
                dateHeureFin: formData.get('dateHeureFin')
            };

            try {
                if (currentSegmentId) {
                    await axios.put(apiUrls.segmentUpdate + currentSegmentId, data);
                } else {
                    await axios.post(apiUrls.segmentCreate, data);
                }

                calendar.refetchEvents();
                closeModal('addSegmentModal');
            } catch (error) {
                console.error('Erreur lors de l\'enregistrement:', error);
                alert('Erreur lors de l\'enregistrement du segment');
            }
        });
    </script>
{% endblock %}
